import { <PERSON>pins, Trirong, Aclonica, Titillium_Web } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import Navbar from "@/Components/Header/Navbar";
import Footer from "@/Components/Footer/Footer";
import { Toaster } from "react-hot-toast";
<<<<<<< HEAD
import { defaultMetadata, organizationStructuredData, websiteStructuredData } from "@/lib/seo-config";
=======
import StructuredData from "@/Components/SEO/StructuredData";
import { defaultMetadata } from "@/lib/seo";
>>>>>>> f6ac638cf17ade5cda99f7a72233f9b88797ff11

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
});

const trirong = Trirong({
  subsets: ["latin"],
  weight: ["400", "700"],
  display: "swap",
  variable: "--font-trirong",
});

const aclonica = Aclonica({
  subsets: ["latin"],
  weight: ["400"],
  variable: "--font-aclonica",
});

const titillium = Titillium_Web({
  subsets: ["latin"],
  weight: ["300", "400", "600"],
  variable: "--font-titillium",
});

<<<<<<< HEAD
export const metadata = defaultMetadata;
=======
export const metadata = {
  title: "Kapoor Software Solutions - Custom Software Development Services",
  description: "Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.",
  metadataBase: new URL('https://kapoorsoftwaresolutions-git-master-valueans-projects.vercel.app'),
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://kapoorsoftwaresolutions-git-master-valueans-projects.vercel.app',
    title: 'Kapoor Software Solutions - Custom Software Development Services',
    description: 'Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.',
    siteName: 'Kapoor Software Solutions',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Kapoor Software Solutions - Custom Software Development Services',
    description: 'Kapoor Software Solutions provides custom software development, mobile app development, web development, AI/ML solutions, and digital transformation services.',
  },
  verification: {
    google: 'your-google-verification-code', // You'll need to replace this with your actual Google verification code
  },
};
>>>>>>> f6ac638cf17ade5cda99f7a72233f9b88797ff11

export default function RootLayout({ children }) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${trirong.variable} ${aclonica.variable} ${titillium.variable}`}
    >
      <head>
        <link rel="icon" href="/Images/favicon.ico" type="image/x-icon" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteStructuredData)
          }}
        />
        <script src="https://analytics.ahrefs.com/analytics.js" data-key="2LzS9D1C5mRlDzjSZCZMKQ" async></script>
        <script src="https://js.stripe.com/v3/pricing-table.js" async></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "s1x1q3sw18");
            `
          }}
        />
      </head>
      <body className={`${poppins.className} text-gray-900 antialiased bg-[#f7fbfb]`}>
        <StructuredData />
        <Navbar />
        <Toaster position="top-center" />
        {children}
        <Footer />
      </body>
    </html>
  );
}
